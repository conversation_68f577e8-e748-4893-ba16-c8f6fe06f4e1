import pytest

# 断言
def test_assertions():
    assert "hello" == "hello"
    assert [1, 2] != [2, 1]
    assert 1 in [1, 2, 3]
    assert "foo" not in "bar"
    with pytest.raises(ZeroDivisionError, match="除数不能为0"):
        1 / 0

# 参数化测试
@pytest.mark.parametrize("input,expected", [
    ("3+5", 8),
    ("2 * 4", 8),
    ("6/3", 2),
])
def test_eval(input, expected):
    assert eval(input) == expected
