from setuptools import setup, find_packages

setup(
    name="base-matrix-python-sdk",                    # 包名（pip install 使用的名称）
    version="3.1.0",                  # 版本号（遵循语义化版本）
    author="hanhai",               # 作者
    author_email="<EMAIL>",
    description="SDK for hanhai python project", # 简短描述
    long_description=open("README.md").read(),  # 详细描述（通常用README）
    long_description_content_type="text/markdown",
    url="http://ezone.kingsoft.com/ksyun/HanHai/Base-Matrix-pyhton-sdk.git", # 项目地址
    packages=find_packages(include=["base*"]),  # 自动发现包
    install_requires=[               # 依赖库
        "sqlalchemy>=2.0.0",
        "pydantic>=2.0.0"
    ],
    extras_require={                  # 可选依赖
        "dev": ["pytest", "black"],
        "async": ["aiomysql"]
    },
    python_requires=">=3.8",         # Python版本要求
    classifiers=[                     # PyPI分类标签
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    include_package_data=True,        # 包含非代码文件
)