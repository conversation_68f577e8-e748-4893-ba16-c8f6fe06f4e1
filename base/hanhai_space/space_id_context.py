from contextvars import ContextVar
import threading


class SpaceIdContext:
    """空间ID上下文管理器（支持异步和同步）"""

    def __init__(self, default_value: str = '0'):
        """
        初始化上下文管理器

        :param default_value: 默认空间ID值
        """
        self._async_ctx: ContextVar[str] = ContextVar("space_id", default=default_value)
        self._sync_ctx = threading.local()
        self._sync_ctx.value = default_value

    def set(self, value: str):
        """设置当前空间ID"""
        try:
            # 异步环境
            return self._async_ctx.set(value)
        except LookupError:
            # 同步环境
            self._sync_ctx.value = value
            return None

    def get(self) -> str:
        """获取当前空间ID"""
        try:
            # 异步环境
            return self._async_ctx.get()
        except LookupError:
            # 同步环境
            return getattr(self._sync_ctx, 'value', '0')

    def reset(self, token):
        """重置上下文（仅异步环境有效）"""
        if token is not None:
            self._async_ctx.reset(token)


# 全局默认实例
global_space_id_ctx = SpaceIdContext()