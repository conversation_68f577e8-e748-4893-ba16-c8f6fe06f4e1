from sqlalchemy import event, and_
from sqlalchemy.sql.selectable import Select, Alias
from sqlalchemy.engine import Engine
import logging
from typing import Callable, Optional
from typing import Callable, Optional, Union, List

logger = logging.getLogger(__name__)


class SpaceIdFilter:
    """spaceId过滤SDK （支持多表）"""

    def __init__(self,
                 table_name: Union[str, List[str]],  # 支持字符串或字符串列表
                 column_name: str,
                 get_value_fn: Callable[[], Optional[str]],
                 debug: bool = False):
        """
        初始化多租户过滤器

        :param table_name: 要过滤的表名（单个表名或表名列表）（如 "task"）
        :param column_name: 用于过滤的字段名（如 "space_id"）
        :param get_value_fn: 获取当前值的函数（无参数，返回int或None）
        :param debug: 是否启用调试日志
        """
        # 统一处理为列表形式
        self.table_names = [table_name] if isinstance(table_name, str) else table_name
        self.table_name = table_name
        self.column_name = column_name
        self.get_value_fn = get_value_fn
        self.debug = debug

    def register(self, engine: Engine):
        """注册事件监听器到指定引擎"""
        if self.debug:
            table_list = ", ".join(self.table_names)
            logger.info(f"注册spaceId过滤器: {table_list}.{self.column_name}")

        @event.listens_for(engine, "before_execute", retval=True)
        def _inject_namespace_filter(conn, clauseelement, multiparams, params):
            return self._filter_impl(clauseelement, multiparams, params)

        return self

    def _filter_impl(self, clauseelement, multiparams, params):
        """实际过滤实现"""
        # 仅处理SELECT查询且包含目标表
        if not isinstance(clauseelement, Select) :
            return clauseelement, multiparams, params

        # 检查是否包含目标表
        stmt_str = str(clauseelement).lower()
        if not any(table.lower() in stmt_str for table in self.table_names):
            return clauseelement, multiparams, params

        # 获取当前值
        value = self.get_value_fn()
        if value is None:
            if self.debug:
                logger.debug(f"未设置 {self.column_name} 值，跳过过滤")
            return clauseelement, multiparams, params

        # 获取表实体
        table_entity = self._find_table_entity(clauseelement)
        if table_entity is None:
            return clauseelement, multiparams, params

        # 动态创建过滤条件
        condition = getattr(table_entity.c, self.column_name) == value

        if self.debug:
            logger.debug(f"动态添加过滤条件: {self.table_name}.{self.column_name} = {value}")

            if clauseelement.whereclause is not None:
                logger.debug(f"原始条件: {self._debug_print(clauseelement.whereclause)}")
            else:
                logger.debug("原始查询无WHERE条件")

        # 添加过滤条件
        if clauseelement.whereclause is not None:
            new_where = and_(clauseelement.whereclause, condition)
        else:
            new_where = condition

        # 创建新查询
        new_stmt = clauseelement.where(new_where)

        if self.debug:
            logger.debug(f"新查询条件: {self._debug_print(new_where)}")

        return new_stmt, multiparams, params

    def _find_table_entity(self, clauseelement):
        """查找目标表实体"""
        from_entities = clauseelement.get_final_froms()
        for entity in from_entities:
            # 处理表别名情况
            actual_entity = entity.original if isinstance(entity, Alias) else entity

            # 确认是目标表且包含目标字段
            if (hasattr(actual_entity, 'name') and
                    actual_entity.name in self.table_names  and # 检查是否在目标表列表中
                    hasattr(actual_entity.c, self.column_name)):
                return actual_entity
        return None

    def _debug_print(self, expression):
        """调试打印SQL表达式"""
        try:
            return str(expression.compile())
        except:
            return str(expression)